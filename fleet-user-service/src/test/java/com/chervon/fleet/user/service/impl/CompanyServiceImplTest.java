package com.chervon.fleet.user.service.impl;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.core.utils.SpringUtils;
import com.chervon.fleet.user.api.entity.dto.CompanyDto;
import com.chervon.fleet.user.api.entity.dto.FleetCompanyPageDto;
import com.chervon.fleet.user.api.entity.enums.*;
import com.chervon.fleet.user.api.entity.error.CompanyErrorCodeEnum;
import com.chervon.fleet.user.api.entity.error.UserErrorCodeEnum;
import com.chervon.fleet.user.api.entity.query.CompanyQuery;
import com.chervon.fleet.user.api.entity.query.UserQuery;
import com.chervon.fleet.user.api.entity.vo.CompanyVo;
import com.chervon.fleet.user.api.entity.vo.FleetCompanyVo;
import com.chervon.fleet.user.entity.po.Company;
import com.chervon.fleet.user.entity.po.User;
import com.chervon.fleet.user.mapper.CompanyMapper;
import com.chervon.fleet.user.service.UserService;
import com.chervon.idgenerator.util.IdUtils;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CompanyServiceImpl 单元测试
 *
 * <AUTHOR> Assistant
 * @since 2024-12-19
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("公司服务实现类单元测试")
class CompanyServiceImplTest {

    @Mock
    private CompanyMapper companyMapper;

    @Mock
    private UserService userService;

    @InjectMocks
    private CompanyServiceImpl companyService;

    /**
     * 初始化MyBatis-Plus Lambda缓存
     */
    public static void initEntityTableInfo(Class<?>... entityClasses) {
        for (Class<?> entityClass : entityClasses) {
            TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), entityClass);
        }
    }

    @BeforeEach
    void setUp() {
        // 初始化实体类缓存
        initEntityTableInfo(Company.class, User.class);
    }

    @Nested
    @DisplayName("分页查询公司信息测试")
    class PagedListTests {

        @Test
        @DisplayName("正常分页查询 - 无邮箱和姓名条件")
        void getPagedList_WithoutEmailAndName_ShouldReturnPageResult() {
            // Given
            CompanyQuery query = new CompanyQuery();
            query.setPageNum(1L);
            query.setPageSize(10L);
            query.setCompanyName("测试公司");

            Company company = new Company()
                    .setId(1L)
                    .setCompanyName("测试公司")
                    .setUserId(100L)
                    .setEnabled(EnableEnum.ENABLED.getType())
                    .setStatus(CompanyStatusEnum.NORMAL.getType());

            Page<Company> mockPage = new Page<>(1, 10);
            mockPage.setRecords(Collections.singletonList(company));
            mockPage.setTotal(1);

            when(companyService.page(any(IPage.class), any(LambdaQueryWrapper.class))).thenReturn(mockPage);

            // When
            PageResult<CompanyVo> result = companyService.getPagedList(query);

            // Then
            assertNotNull(result);
            assertEquals(1, result.getTotal());
            assertEquals(1, result.getPageNum());
            assertEquals(10, result.getPageSize());
            assertEquals(1, result.getList().size());
            assertEquals("测试公司", result.getList().get(0).getCompanyName());
        }

        @Test
        @DisplayName("分页查询 - 包含邮箱条件")
        void getPagedList_WithEmailCondition_ShouldQueryUserFirst() {
            // Given
            CompanyQuery query = new CompanyQuery();
            query.setPageNum(1L);
            query.setPageSize(10L);
            query.setEmail("<EMAIL>");

            User user = new User();
            user.setId(100L);
            user.setEmail("<EMAIL>");

            when(userService.getList(any(UserQuery.class))).thenReturn(Collections.singletonList(user));

            Page<Company> mockPage = new Page<>(1, 10);
            mockPage.setRecords(new ArrayList<>());
            mockPage.setTotal(0);

            when(companyService.page(any(IPage.class), any(LambdaQueryWrapper.class))).thenReturn(mockPage);

            // When
            PageResult<CompanyVo> result = companyService.getPagedList(query);

            // Then
            assertNotNull(result);
            assertEquals(0, result.getTotal());
            verify(userService).getList(any(UserQuery.class));
        }

        @Test
        @DisplayName("分页查询 - 包含姓名条件")
        void getPagedList_WithNameCondition_ShouldQueryUserFirst() {
            // Given
            CompanyQuery query = new CompanyQuery();
            query.setPageNum(1L);
            query.setPageSize(10L);
            query.setName("张三");

            User user = new User();
            user.setId(100L);

            when(userService.getList(any(UserQuery.class))).thenReturn(Collections.singletonList(user));

            Page<Company> mockPage = new Page<>(1, 10);
            mockPage.setRecords(new ArrayList<>());
            mockPage.setTotal(0);

            when(companyService.page(any(IPage.class), any(LambdaQueryWrapper.class))).thenReturn(mockPage);

            // When
            PageResult<CompanyVo> result = companyService.getPagedList(query);

            // Then
            assertNotNull(result);
            verify(userService).getList(any(UserQuery.class));
        }

        @Test
        @DisplayName("分页查询 - 查询结果为空")
        void getPagedList_WithEmptyResult_ShouldReturnEmptyList() {
            // Given
            CompanyQuery query = new CompanyQuery();
            query.setPageNum(1L);
            query.setPageSize(10L);

            Page<Company> mockPage = new Page<>(1, 10);
            mockPage.setRecords(new ArrayList<>());
            mockPage.setTotal(0);

            when(companyService.page(any(IPage.class), any(LambdaQueryWrapper.class))).thenReturn(mockPage);

            // When
            PageResult<CompanyVo> result = companyService.getPagedList(query);

            // Then
            assertNotNull(result);
            assertEquals(0, result.getTotal());
            assertTrue(result.getList().isEmpty());
        }
    }

    @Nested
    @DisplayName("根据ID查询公司详情测试")
    class GetDetailTests {

        @Test
        @DisplayName("正常查询 - 公司存在")
        void getDetail_WithValidId_ShouldReturnCompanyVo() {
            // Given
            Long companyId = 1L;
            Company company = new Company()
                    .setId(companyId)
                    .setCompanyName("测试公司")
                    .setUserId(100L)
                    .setEnabled(EnableEnum.ENABLED.getType())
                    .setStatus(CompanyStatusEnum.NORMAL.getType());

            when(companyService.getOne(any(LambdaQueryWrapper.class))).thenReturn(company);

            // When
            CompanyVo result = companyService.getDetail(companyId);

            // Then
            assertNotNull(result);
            assertEquals(companyId, result.getId());
            assertEquals("测试公司", result.getCompanyName());
            assertEquals(100L, result.getUserId());
        }

        @Test
        @DisplayName("查询不存在的公司 - 返回null")
        void getDetail_WithInvalidId_ShouldReturnNull() {
            // Given
            Long companyId = 999L;
            when(companyService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

            // When
            CompanyVo result = companyService.getDetail(companyId);

            // Then
            assertNull(result);
        }
    }

    @Nested
    @DisplayName("根据名称查询公司测试")
    class GetCompanyByNameTests {

        @Test
        @DisplayName("正常查询 - 公司存在")
        void getCompanyByName_WithValidName_ShouldReturnCompanyVo() {
            // Given
            String companyName = "测试公司";
            Company company = new Company()
                    .setId(1L)
                    .setCompanyName(companyName)
                    .setUserId(100L)
                    .setEnabled(EnableEnum.ENABLED.getType())
                    .setStatus(CompanyStatusEnum.NORMAL.getType());

            when(companyService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.singletonList(company));

            // When
            CompanyVo result = companyService.getCompanyByName(companyName);

            // Then
            assertNotNull(result);
            assertEquals(1L, result.getId());
            assertEquals(companyName, result.getCompanyName());
        }

        @Test
        @DisplayName("查询不存在的公司名称 - 返回null")
        void getCompanyByName_WithInvalidName_ShouldReturnNull() {
            // Given
            String companyName = "不存在的公司";
            when(companyService.list(any(LambdaQueryWrapper.class))).thenReturn(new ArrayList<>());

            // When
            CompanyVo result = companyService.getCompanyByName(companyName);

            // Then
            assertNull(result);
        }
    }
}
